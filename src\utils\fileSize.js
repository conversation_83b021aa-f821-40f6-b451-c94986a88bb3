/**
 * 文件大小格式化工具函数
 * 将字节数转换为更易读的单位格式
 */

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 * 
 * @example
 * formatFileSize(1024) // "1 KB"
 * formatFileSize(1048576) // "1 MB"
 * formatFileSize(2147483648) // "2 GB"
 * formatFileSize(1536) // "1.5 KB"
 * formatFileSize(5242880) // "5 MB"
 */
export function formatFileSize(bytes) {
  // 处理无效输入
  if (!bytes || bytes === 0) return '0 B';
  if (typeof bytes !== 'number' || bytes < 0) return '未知大小';
  
  // 定义单位数组
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  
  // 计算单位级别
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  // 确保不超出单位数组范围
  const unitIndex = Math.min(i, units.length - 1);
  
  // 计算转换后的数值
  const size = bytes / Math.pow(k, unitIndex);
  
  // 格式化数值
  let formattedSize;
  if (unitIndex === 0) {
    // 字节不显示小数
    formattedSize = size.toString();
  } else if (size >= 100) {
    // 大于等于100时不显示小数
    formattedSize = Math.round(size).toString();
  } else if (size >= 10) {
    // 10-99之间显示1位小数
    formattedSize = size.toFixed(1);
  } else {
    // 小于10显示2位小数
    formattedSize = size.toFixed(2);
  }
  
  // 移除末尾的0和小数点
  formattedSize = formattedSize.replace(/\.?0+$/, '');
  
  return `${formattedSize} ${units[unitIndex]}`;
}

/**
 * 批量格式化文件大小
 * @param {Array} files - 包含size属性的文件对象数组
 * @returns {Array} 添加了formattedSize属性的文件对象数组
 */
export function formatFileSizes(files) {
  return files.map(file => ({
    ...file,
    formattedSize: formatFileSize(file.size)
  }));
}

/**
 * 常用文件大小常量（字节）
 */
export const FILE_SIZE_CONSTANTS = {
  KB: 1024,
  MB: 1024 * 1024,
  GB: 1024 * 1024 * 1024,
  TB: 1024 * 1024 * 1024 * 1024
};

/**
 * 文件大小比较函数
 * @param {number} size1 - 第一个文件大小（字节）
 * @param {number} size2 - 第二个文件大小（字节）
 * @returns {number} 比较结果：-1, 0, 1
 */
export function compareFileSize(size1, size2) {
  if (size1 < size2) return -1;
  if (size1 > size2) return 1;
  return 0;
}

/**
 * 检查文件大小是否超过限制
 * @param {number} fileSize - 文件大小（字节）
 * @param {number} maxSize - 最大允许大小（字节）
 * @returns {boolean} 是否超过限制
 */
export function isFileSizeExceeded(fileSize, maxSize) {
  return fileSize > maxSize;
}

/**
 * 获取文件大小的单位
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 单位字符串
 */
export function getFileSizeUnit(bytes) {
  if (!bytes || bytes === 0) return 'B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const unitIndex = Math.min(i, units.length - 1);
  
  return units[unitIndex];
}
