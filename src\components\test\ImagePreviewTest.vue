<template>
  <div class="image-preview-test">
    <h2>图片预览功能测试</h2>
    <p>这是一个测试组件，用于验证图片预览功能的实现</p>
    
    <div class="test-info">
      <h3>实现的功能：</h3>
      <ul>
        <li>✅ 支持多种图片格式：jpg, jpeg, png, gif, bmp, webp, svg</li>
        <li>✅ 图片缩放功能（25% - 300%）</li>
        <li>✅ 全屏预览模式</li>
        <li>✅ 鼠标滚轮缩放</li>
        <li>✅ 图片加载状态显示</li>
        <li>✅ 错误处理和重试机制</li>
        <li>✅ 图片元数据显示</li>
        <li>✅ 响应式设计</li>
        <li>✅ 深色/浅色主题适配</li>
      </ul>
    </div>
    
    <div class="test-methods">
      <h3>新增的方法：</h3>
      <ul>
        <li><code>isImageFile(fileType)</code> - 判断是否为图片文件</li>
        <li><code>resetImagePreviewState()</code> - 重置图片预览状态</li>
        <li><code>zoomIn() / zoomOut() / resetZoom()</code> - 缩放控制</li>
        <li><code>onImageLoad() / onImageError()</code> - 图片加载回调</li>
        <li><code>toggleFullscreen()</code> - 切换全屏预览</li>
        <li><code>handleMouseWheel()</code> - 鼠标滚轮缩放</li>
        <li><code>downloadCurrentImage()</code> - 下载当前图片</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreviewTest'
}
</script>

<style scoped>
.image-preview-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info, .test-methods {
  margin: 20px 0;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #f8fafc;
}

.test-info h3, .test-methods h3 {
  margin-top: 0;
  color: #2c3e50;
}

.test-info ul, .test-methods ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li, .test-methods li {
  margin: 8px 0;
  line-height: 1.5;
}

code {
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}
</style>
