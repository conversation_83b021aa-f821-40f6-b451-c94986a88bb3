<template>
  <div class="file-size-test">
    <h2>文件大小格式化功能测试</h2>
    
    <div class="test-section">
      <h3>格式化规则：</h3>
      <ul class="rules-list">
        <li><strong>字节 (B)</strong>：不显示小数，如 "512 B"</li>
        <li><strong>KB/MB/GB</strong>：
          <ul>
            <li>≥ 100：不显示小数，如 "156 MB"</li>
            <li>10-99：显示1位小数，如 "45.6 KB"</li>
            <li>< 10：显示2位小数，如 "2.34 MB"</li>
          </ul>
        </li>
        <li><strong>自动移除</strong>：末尾的0和小数点</li>
      </ul>
    </div>
    
    <div class="test-section">
      <h3>测试用例：</h3>
      <div class="test-cases">
        <div class="test-case" v-for="testCase in testCases" :key="testCase.input">
          <div class="input">
            <span class="label">输入：</span>
            <span class="value">{{ testCase.input.toLocaleString() }} 字节</span>
          </div>
          <div class="output">
            <span class="label">输出：</span>
            <span class="value formatted">{{ formatFileSize(testCase.input) }}</span>
          </div>
          <div class="expected">
            <span class="label">期望：</span>
            <span class="value">{{ testCase.expected }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>自定义测试：</h3>
      <div class="custom-test">
        <el-input
          v-model="customInput"
          placeholder="请输入字节数"
          type="number"
          style="width: 200px; margin-right: 10px;"
        ></el-input>
        <span class="result">{{ formatFileSize(Number(customInput)) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileSizeFormatTest',
  data() {
    return {
      customInput: '1024',
      testCases: [
        { input: 0, expected: '0 B' },
        { input: 512, expected: '512 B' },
        { input: 1024, expected: '1 KB' },
        { input: 1536, expected: '1.5 KB' },
        { input: 2048, expected: '2 KB' },
        { input: 5120, expected: '5 KB' },
        { input: 10240, expected: '10 KB' },
        { input: 15360, expected: '15 KB' },
        { input: 102400, expected: '100 KB' },
        { input: 1048576, expected: '1 MB' },
        { input: 1572864, expected: '1.5 MB' },
        { input: 5242880, expected: '5 MB' },
        { input: 10485760, expected: '10 MB' },
        { input: 104857600, expected: '100 MB' },
        { input: 1073741824, expected: '1 GB' },
        { input: 2147483648, expected: '2 GB' },
        { input: 1099511627776, expected: '1 TB' }
      ]
    };
  },
  methods: {
    /**
     * 格式化文件大小（与主组件中的方法相同）
     */
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 B';
      if (typeof bytes !== 'number' || bytes < 0) return '未知大小';
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      const k = 1024;
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      const unitIndex = Math.min(i, units.length - 1);
      const size = bytes / Math.pow(k, unitIndex);
      
      let formattedSize;
      if (unitIndex === 0) {
        formattedSize = size.toString();
      } else if (size >= 100) {
        formattedSize = Math.round(size).toString();
      } else if (size >= 10) {
        formattedSize = size.toFixed(1);
      } else {
        formattedSize = size.toFixed(2);
      }
      
      formattedSize = formattedSize.replace(/\.?0+$/, '');
      return `${formattedSize} ${units[unitIndex]}`;
    }
  }
}
</script>

<style scoped>
.file-size-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #f8fafc;
}

.test-section h3 {
  margin-top: 0;
  color: #2c3e50;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.rules-list {
  margin: 0;
  padding-left: 20px;
  line-height: 1.6;
}

.rules-list li {
  margin: 8px 0;
}

.rules-list ul {
  margin: 8px 0;
  padding-left: 20px;
}

.test-cases {
  display: grid;
  gap: 16px;
}

.test-case {
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  align-items: center;
}

.test-case .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.test-case .value {
  font-family: 'Courier New', monospace;
}

.test-case .formatted {
  color: #409eff;
  font-weight: 600;
}

.custom-test {
  display: flex;
  align-items: center;
  gap: 16px;
}

.custom-test .result {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  font-family: 'Courier New', monospace;
}

@media (max-width: 768px) {
  .test-case {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .custom-test {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
