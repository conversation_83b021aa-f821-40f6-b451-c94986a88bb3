<template>
  <div class="reportAOpinions">
    <div class="content">
      <el-card class="upload-card">
        <div slot="header" class="btns">
          <el-upload
            class="upload-demo"
            :action="`${prefix}/attachment/uploadFile`"
            :headers="uploadHeaders"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :on-success="handleSuccess"
            :file-list="fileList"
            :show-file-list="false"
          >
            <el-button type="primary" class="confirm-btn" v-if="id"
              >上传</el-button
            >
          </el-upload>
          <el-button type="primary" class="confirm-btn" @click="handleCancle"
            >返回</el-button
          >
          <el-button
            type="primary"
            class="confirm-btn"
            @click="handleSubmit"
            v-if="id"
            >确定</el-button
          >
        </div>
        <div class="file-list">
          <template v-if="files.length > 0">
            <div
              class="file-item"
              :class="{ 'file-item-selected': currentFileId === file.fileId }"
              v-for="(file, index) in files"
              :key="index"
              @click="handlePreview(file)"
            >
              <div class="file-icon">
                <div class="icon-placeholder" :class="getFileTypeClass(file.originalName)">
                  <span class="file-icon-text">{{ getFileIcon(file.originalName) }}</span>
                </div>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.originalName }}</div>
                <div class="file-meta">
                  <span>文件大小：{{ file.size }}</span>
                  <span>上传人：{{ file.createBy }}</span>
                  <span>上传时间：{{ file.createTime }}</span>
                </div>
              </div>
              <div class="file-actions">
                <div
                  class="action-btn download-btn"
                  @click="handleDownload(file)"
                >下载</div>
                <div
                  class="action-btn preview-btn"
                  @click="handlePreview(file)"
                >预览</div>
                <div
                  class="action-btn delete-btn"
                  @click="handleDelete(file, index)"
                >删除</div>
              </div>
            </div>
          </template>
          <div v-else class="empty-state">
            <div class="empty-icon">📁</div>
            <div class="empty-text">暂无文件</div>
            <div class="empty-desc">您可以点击上方的上传按钮添加文件</div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="preview-box">
      <!-- PDF预览 -->
      <pdf-viewer
        v-if="fileUrl && fileType === 'pdf'"
        :pdf-url="fileUrl"
      ></pdf-viewer>

      <!-- Word文档预览 -->
      <word-viewer
        v-if="fileUrl && (fileType === 'docx' || fileType === 'doc')"
        :file-url="fileUrl"
        :file-type="fileType"
      ></word-viewer>

      <!-- 图片预览 -->
      <div
        v-if="fileUrl && isImageFile(fileType)"
        class="image-preview-container"
      >
        <div class="image-preview-header">
          <h3 class="preview-title">{{ currentFileName }}</h3>
          <div class="image-controls">
            <el-button
              size="mini"
              icon="el-icon-zoom-in"
              @click="zoomIn"
              :disabled="zoomLevel >= 300"
            >放大</el-button>
            <el-button
              size="mini"
              icon="el-icon-zoom-out"
              @click="zoomOut"
              :disabled="zoomLevel <= 25"
            >缩小</el-button>
            <el-button
              size="mini"
              icon="el-icon-refresh"
              @click="resetZoom"
            >重置</el-button>
            <el-button
              size="mini"
              icon="el-icon-download"
              @click="downloadCurrentImage"
            >下载</el-button>
          </div>
        </div>

        <div class="image-preview-content" ref="imageContainer">
          <div
            class="image-wrapper"
            :style="{ transform: `scale(${zoomLevel / 100})` }"
          >
            <img
              :src="fileUrl"
              :alt="currentFileName"
              class="preview-image"
              @load="onImageLoad"
              @error="onImageError"
              @click="toggleFullscreen"
            />
          </div>

          <!-- 加载状态 -->
          <div v-if="imageLoading" class="image-loading">
            <el-icon class="is-loading">
              <i class="el-icon-loading"></i>
            </el-icon>
            <p>图片加载中...</p>
          </div>

          <!-- 错误状态 -->
          <div v-if="imageError" class="image-error">
            <el-icon>
              <i class="el-icon-picture-outline"></i>
            </el-icon>
            <p>图片加载失败</p>
            <el-button size="mini" @click="retryLoadImage">重试</el-button>
          </div>
        </div>

        <!-- 图片信息 -->
        <div class="image-info" v-if="imageMetadata">
          <div class="info-item">
            <span class="label">尺寸：</span>
            <span class="value">{{ imageMetadata.width }} × {{ imageMetadata.height }}</span>
          </div>
          <div class="info-item">
            <span class="label">缩放：</span>
            <span class="value">{{ zoomLevel }}%</span>
          </div>
          <div class="info-item">
            <span class="label">格式：</span>
            <span class="value">{{ fileType.toUpperCase() }}</span>
          </div>
        </div>
      </div>

      <!-- 全屏预览模态框 -->
      <el-dialog
        :visible.sync="fullscreenVisible"
        :show-close="true"
        :close-on-click-modal="true"
        :close-on-press-escape="true"
        custom-class="fullscreen-image-dialog"
        width="90%"
        top="5vh"
      >
        <div class="fullscreen-image-container">
          <img
            :src="fileUrl"
            :alt="currentFileName"
            class="fullscreen-image"
            @wheel="handleMouseWheel"
            :style="{ transform: `scale(${fullscreenZoom / 100})` }"
          />
        </div>
        <div slot="footer" class="fullscreen-controls">
          <el-button @click="fullscreenZoomOut">缩小</el-button>
          <span class="zoom-display">{{ fullscreenZoom }}%</span>
          <el-button @click="fullscreenZoomIn">放大</el-button>
          <el-button @click="resetFullscreenZoom">重置</el-button>
        </div>
      </el-dialog>

      <!-- 默认空状态 -->
      <div v-if="!fileUrl" class="preview-empty">
        <div class="empty-icon">📄</div>
        <div class="empty-text">请选择文件进行预览</div>
      </div>
    </div>
  </div>
</template>
<script>
import PdfViewer from "@/components/common/pdfView.vue";
import WordViewer from "@/components/common/wordView.vue";
import {
  getFileList,
  downloadFileById,
  previewFileById,
} from "@/api/file/index.js";
import { saveFilterChemicals } from "@/api/ogwActiveRecord/chemicalsServe.js";
import { downFileUtil } from "@/utils/file.js";
export default {
  name: "reportAOpinions",
  components: {
    PdfViewer,
    WordViewer,
  },
  activated() {
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    this.files = [];
    this.fileUrl = null;
    this.currentFileId = null; // 重置选中状态
    if (fileIds) {
      this.getFile(fileIds);
    }
    this.initUploadHeaders();
  },
  mounted() {
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    if (fileIds) {
      this.getFile(fileIds);
    }
    // 获取.env.production中的VUE_APP_PREFIX
    this.prefix = process.env.VUE_APP_PREFIX;
    this.token = localStorage.getItem("access_token");
    this.initUploadHeaders();
  },
  data() {
    return {
      fileType: "",
      fileUrl: "",
      id: "",
      fileList: [],
      files: [],
      form: [],
      formType: null,
      prefix: "",
      token: "",
      uploadHeaders: {},
      currentFileId: null, // 当前选中的文件ID
      currentFileName: "", // 当前选中的文件名
      // 图片预览相关数据
      zoomLevel: 100, // 缩放级别
      imageLoading: false, // 图片加载状态
      imageError: false, // 图片错误状态
      imageMetadata: null, // 图片元数据
      fullscreenVisible: false, // 全屏预览显示状态
      fullscreenZoom: 100, // 全屏缩放级别
      // 支持的图片格式
      imageFormats: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
    };
  },
  methods: {
    /**
     * 初始化上传请求头，添加身份验证token
     */
    initUploadHeaders() {
      const access_token = localStorage.getItem("access_token");
      this.uploadHeaders = {
        client_id: process.env.VUE_APP_ID,
        client_secret: process.env.VUE_APP_ID,
      };
      if (access_token) {
        this.uploadHeaders.Authorization = access_token;
      }
    },
    /**
     * 根据文件名获取文件图标
     */
    getFileIcon(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        'pdf': '📄',
        'doc': '📝',
        'docx': '📝',
        'xls': '📊',
        'xlsx': '📊',
        'ppt': '📋',
        'pptx': '📋',
        'txt': '📄',
        'jpg': '🖼️',
        'jpeg': '🖼️',
        'png': '🖼️',
        'gif': '🖼️',
        'zip': '📦',
        'rar': '📦',
        '7z': '📦'
      };
      return iconMap[ext] || '📄';
    },
    /**
     * 根据文件名获取文件类型样式类
     */
    getFileTypeClass(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        'pdf': 'file-type-pdf',
        'doc': 'file-type-word',
        'docx': 'file-type-word',
        'xls': 'file-type-excel',
        'xlsx': 'file-type-excel',
        'ppt': 'file-type-ppt',
        'pptx': 'file-type-ppt',
        'txt': 'file-type-text',
        'jpg': 'file-type-image',
        'jpeg': 'file-type-image',
        'png': 'file-type-image',
        'gif': 'file-type-image',
        'zip': 'file-type-archive',
        'rar': 'file-type-archive',
        '7z': 'file-type-archive'
      };
      return typeMap[ext] || 'file-type-default';
    },
    handleSubmit() {
      const data = {
        id: this.id,
        reportFileId: this.form.join(","),
      };
      this.saveTableRow(data);
    },
    handleCancle() {
      this.$router.back();
    },
    handleExceed(files, fileList) {
      this.$message.warning("最多只能上传3个文件");
    },
    handleSuccess(response, file, fileList) {
      this.$message.success("上传成功");
      this.files.push(response.data);
      this.form.push(response.data.fileId);
    },
    async handleDownload(file) {
      const res = await downloadFileById(file.fileId);
      if (res) {
        downFileUtil(res, file.originalName);
      }
    },
    async handlePreview(file) {
      this.currentFileId = file.fileId; // 设置当前选中的文件ID
      this.currentFileName = file.originalName; // 设置当前文件名
      this.fileType = file.originalName.split(".").pop().toLowerCase();

      // 重置图片预览状态
      this.resetImagePreviewState();

      const id = file.fileId;

      // 如果是图片文件，显示加载状态
      if (this.isImageFile(this.fileType)) {
        this.imageLoading = true;
      }

      const res = await previewFileById(id);
      if (res.code === 200) {
        this.fileUrl = res.data;

        // 如果是图片文件，加载完成后隐藏加载状态
        if (this.isImageFile(this.fileType)) {
          this.imageLoading = false;
        }
      } else {
        // 预览失败处理
        if (this.isImageFile(this.fileType)) {
          this.imageLoading = false;
          this.imageError = true;
        }
        this.$message.error('文件预览失败');
      }
    },
    handleDelete(file, index) {
      console.log("删除文件:", file.name);
      this.$confirm("确认删除该文件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.files.splice(index, 1);
          this.$message.success("删除成功");
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    async getFile(id) {
      const res = await getFileList(id);
      if (res.code === 200) {
        this.files = res.data;
        // 如果文件列表不为空，自动打开第一个文件
        if (this.files.length > 0) {
          await this.handlePreview(this.files[0]);
        }
      }
    },
    async saveTableRow(data) {
      let res = null;
      switch (this.formType) {
        case "filter":
          data.type = 1;
          res = await saveFilterChemicals(data);
          break;
        case "evaluate":
          data.type = 2;
          res = await saveFilterChemicals(data);
          break;
      }
      if (res.code === 200) {
        this.$message.success("保存成功");
      }
    },

    // ========== 图片预览相关方法 ==========

    /**
     * 判断是否为图片文件
     */
    isImageFile(fileType) {
      return this.imageFormats.includes(fileType.toLowerCase());
    },

    /**
     * 重置图片预览状态
     */
    resetImagePreviewState() {
      this.zoomLevel = 100;
      this.imageLoading = false;
      this.imageError = false;
      this.imageMetadata = null;
      this.fullscreenVisible = false;
      this.fullscreenZoom = 100;
    },

    /**
     * 放大图片
     */
    zoomIn() {
      if (this.zoomLevel < 300) {
        this.zoomLevel += 25;
      }
    },

    /**
     * 缩小图片
     */
    zoomOut() {
      if (this.zoomLevel > 25) {
        this.zoomLevel -= 25;
      }
    },

    /**
     * 重置缩放
     */
    resetZoom() {
      this.zoomLevel = 100;
    },

    /**
     * 图片加载成功回调
     */
    onImageLoad(event) {
      this.imageLoading = false;
      this.imageError = false;

      // 获取图片元数据
      const img = event.target;
      this.imageMetadata = {
        width: img.naturalWidth,
        height: img.naturalHeight
      };
    },

    /**
     * 图片加载失败回调
     */
    onImageError() {
      this.imageLoading = false;
      this.imageError = true;
      this.imageMetadata = null;
    },

    /**
     * 重试加载图片
     */
    retryLoadImage() {
      this.imageError = false;
      this.imageLoading = true;

      // 重新设置图片src来触发重新加载
      const currentUrl = this.fileUrl;
      this.fileUrl = '';
      this.$nextTick(() => {
        this.fileUrl = currentUrl;
      });
    },

    /**
     * 切换全屏预览
     */
    toggleFullscreen() {
      if (!this.imageError && !this.imageLoading) {
        this.fullscreenVisible = true;
        this.fullscreenZoom = 100;
      }
    },

    /**
     * 全屏模式放大
     */
    fullscreenZoomIn() {
      if (this.fullscreenZoom < 500) {
        this.fullscreenZoom += 25;
      }
    },

    /**
     * 全屏模式缩小
     */
    fullscreenZoomOut() {
      if (this.fullscreenZoom > 25) {
        this.fullscreenZoom -= 25;
      }
    },

    /**
     * 重置全屏缩放
     */
    resetFullscreenZoom() {
      this.fullscreenZoom = 100;
    },

    /**
     * 鼠标滚轮缩放处理
     */
    handleMouseWheel(event) {
      event.preventDefault();

      if (event.deltaY < 0) {
        // 向上滚动，放大
        this.fullscreenZoomIn();
      } else {
        // 向下滚动，缩小
        this.fullscreenZoomOut();
      }
    },

    /**
     * 下载当前图片
     */
    async downloadCurrentImage() {
      if (this.currentFileId) {
        const currentFile = this.files.find(file => file.fileId === this.currentFileId);
        if (currentFile) {
          await this.handleDownload(currentFile);
        }
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.reportAOpinions {
  background: #fff;
  display: flex;
  .preview-box {
    flex: 1;
    height: 100%;
    border-left: 1px solid #ccc;
  }
  .content {
    height: auto;
    flex: 1;
    // padding: 20px;
    .upload-card {
      max-width: 800px;
      height: 100%;
      margin: 0 auto;
      .btns {
        display: flex;
      }
      .confirm-btn {
        margin-left: 12px;
      }

      .device-section {
        padding: 0 20px;
      }

      .el-form-item {
        margin-bottom: 22px;
      }
    }
  }
}

/* 深色主题适配 - reportAOpinions 主容器 */
html[data-theme='dark'] {
  .reportAOpinions {
    background-color: #0c1324;
    color: #ffffff;

    .preview-box {
      border-left: 1px solid #4F98F6;
      background-color: #1A2E52;
    }

    .content {
      background-color: #0c1324;

      .upload-card {
        background-color: #1A2E52;
        border: 1px solid #4F98F6;

        ::v-deep .el-card__header {
          background-color: #162549;
          border-bottom: 1px solid #4F98F6;
          color: #ffffff;
        }

        ::v-deep .el-card__body {
          background-color: #1A2E52;
          color: #ffffff;
        }
      }
    }
  }
}

/* 浅色主题适配 - reportAOpinions 主容器 */
[data-theme="tint"],
[data-theme="defaule"] {
  .reportAOpinions {
    background-color: #ffffff;
    color: #2E3641;

    .preview-box {
      border-left: 1px solid #EAEFF5;
      background-color: #ffffff;
    }

    .content {
      background-color: #ffffff;

      .upload-card {
        background-color: #ffffff;
        border: 1px solid #EAEFF5;

        ::v-deep .el-card__header {
          background-color: #F8FAFC;
          border-bottom: 1px solid #EAEFF5;
          color: #2E3641;
        }

        ::v-deep .el-card__body {
          background-color: #ffffff;
          color: #2E3641;
        }
      }
    }
  }
}

/* 文件列表容器 */
.file-list {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 文件列表项 */
.file-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 文件选中状态样式 */
.file-item-selected {
  background-color: #e6f7ff !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2) !important;
}

.file-item-selected:hover {
  background-color: #d6f3ff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 文件图标区域 */
.file-icon {
  margin-right: 16px;
  position: relative;
}

.icon-placeholder {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.file-icon-text {
  font-size: 20px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 不同文件类型的图标样式 */
.file-type-pdf {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.file-type-word {
  background: linear-gradient(135deg, #4dabf7 0%, #339af0 100%);
  box-shadow: 0 2px 8px rgba(77, 171, 247, 0.3);
}

.file-type-excel {
  background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
  box-shadow: 0 2px 8px rgba(81, 207, 102, 0.3);
}

.file-type-ppt {
  background: linear-gradient(135deg, #ff8cc8 0%, #ff6b9d 100%);
  box-shadow: 0 2px 8px rgba(255, 140, 200, 0.3);
}

.file-type-image {
  background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
  box-shadow: 0 2px 8px rgba(255, 212, 59, 0.3);
}

.file-type-archive {
  background: linear-gradient(135deg, #9775fa 0%, #845ef7 100%);
  box-shadow: 0 2px 8px rgba(151, 117, 250, 0.3);
}

.file-type-text {
  background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
  box-shadow: 0 2px 8px rgba(116, 192, 252, 0.3);
}

.file-type-default {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 2px dashed #e2e8f0;
  margin: 20px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.5;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
  }

  .file-icon {
    margin-right: 0;
    margin-bottom: 12px;
    align-self: center;
  }

  .file-info {
    width: 100%;
    text-align: center;
    margin-bottom: 16px;
  }

  .file-actions {
    margin-left: 0;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-btn {
    flex: 1;
    min-width: 80px;
  }

  .file-meta {
    justify-content: center;
    flex-direction: column;
    gap: 4px;
  }

  .file-meta span {
    padding-left: 0;
  }

  .file-meta span::before {
    display: none;
  }
}

/* 文件信息区域 */
.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.4;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  font-size: 12px;
  color: #8492a6;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
}

.file-meta span {
  position: relative;
  padding-left: 16px;
}

.file-meta span::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: #d1d5db;
  border-radius: 50%;
}

.file-meta span:first-child {
  padding-left: 0;
}

.file-meta span:first-child::before {
  display: none;
}

/* 操作按钮区域 */
.file-actions {
  flex-shrink: 0;
  margin-left: 16px;
  display: flex;
  gap: 8px;
}

.action-btn {
  min-width: 64px;
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

/* 下载按钮样式 */
.download-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-color: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.download-btn:hover {
  background: linear-gradient(135deg, #3b8bfe 0%, #00d4fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4);
}

.download-btn:active {
  transform: translateY(0);
}

/* 预览按钮样式 */
.preview-btn {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
  border-color: #a8edea;
  box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);
}

.preview-btn:hover {
  background: linear-gradient(135deg, #96e6e1 0%, #fcc9d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(168, 237, 234, 0.4);
}

.preview-btn:active {
  transform: translateY(0);
}

/* 删除按钮样式 */
.delete-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #e53e3e;
  border-color: #ff9a9e;
  box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #ff8a8e 0%, #fdbfdf 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4);
}

.delete-btn:active {
  transform: translateY(0);
}

/* 深色主题样式 */
html[data-theme='dark'] {
  .file-list {
    border-color: #4F98F6;
    background: #1A2E52;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.2);
  }

  .file-item {
    border-bottom-color: rgba(79, 152, 246, 0.3);
  }

  .file-item:hover {
    background-color: rgba(79, 152, 246, 0.1);
    box-shadow: 0 4px 12px rgba(79, 152, 246, 0.2);
  }

  /* 深色主题下的文件选中状态 */
  .file-item-selected {
    background-color: rgba(79, 152, 246, 0.2) !important;
    border-left: 4px solid #4F98F6 !important;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4) !important;
  }

  .file-item-selected:hover {
    background-color: rgba(79, 152, 246, 0.3) !important;
    box-shadow: 0 4px 12px rgba(79, 152, 246, 0.5) !important;
  }

  .icon-placeholder {
    background: linear-gradient(135deg, #4F98F6 0%, #6BA4F4 100%);
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4);
  }

  .file-name {
    color: #ffffff;
  }

  .file-meta {
    color: #B3D3E5;
  }

  .file-meta span::before {
    background-color: #6493D4;
  }

  .download-btn {
    background: linear-gradient(135deg, #4F98F6 0%, #6BA4F4 100%);
    border-color: #4F98F6;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4);
  }

  .download-btn:hover {
    background: linear-gradient(135deg, #3578F6 0%, #5B94F4 100%);
    box-shadow: 0 4px 16px rgba(79, 152, 246, 0.5);
  }

  .preview-btn {
    background: linear-gradient(135deg, #639EF6 0%, #7BAAF6 100%);
    color: #ffffff;
    border-color: #639EF6;
    box-shadow: 0 2px 8px rgba(99, 158, 246, 0.4);
  }

  .preview-btn:hover {
    background: linear-gradient(135deg, #5B94F4 0%, #73A4F6 100%);
    box-shadow: 0 4px 16px rgba(99, 158, 246, 0.5);
  }

  .delete-btn {
    background: linear-gradient(135deg, #F74A4D 0%, #FF6B6E 100%);
    color: #ffffff;
    border-color: #F74A4D;
    box-shadow: 0 2px 8px rgba(247, 74, 77, 0.4);
  }

  .delete-btn:hover {
    background: linear-gradient(135deg, #E53E3E 0%, #F56565 100%);
    box-shadow: 0 4px 16px rgba(247, 74, 77, 0.5);
  }

  .empty-state {
    background: linear-gradient(135deg, #1A2E52 0%, #2A3F6B 100%);
    border-color: rgba(79, 152, 246, 0.3);
  }

  .empty-text {
    color: #ffffff;
  }

  .empty-desc {
    color: #B3D3E5;
  }
}

/* 浅色主题（tint）样式增强 */
[data-theme="tint"],
[data-theme="defaule"] {
  .file-list {
    border-color: #EAEFF5;
    background: #ffffff;
  }

  .file-item:hover {
    background-color: #F8FAFC;
  }

  /* 浅色主题下的文件选中状态 */
  .file-item-selected {
    background-color: #E6F7FF !important;
    border-left: 4px solid #409EFF !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  }

  .file-item-selected:hover {
    background-color: #D6F3FF !important;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25) !important;
  }

  .file-name {
    color: #2E3641;
  }

  .file-meta {
    color: #97A0AA;
  }

  .icon-placeholder {
    background: linear-gradient(135deg, #4EA0FD 0%, #639EF6 100%);
  }

  /* 浅色主题下的简洁按钮样式 */
  .download-btn {
    background: #409eff;
    color: white;
    border-color: #409eff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  .download-btn:hover {
    background: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }

  .preview-btn {
    background: #67c23a;
    color: white;
    border-color: #67c23a;
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
  }

  .preview-btn:hover {
    background: #529b2e;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
  }

  .delete-btn {
    background: #f56c6c;
    color: white;
    border-color: #f56c6c;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
  }

  .delete-btn:hover {
    background: #dd6161;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.3);
  }
}

/* ========== 图片预览样式 ========== */

/* 图片预览容器 */
.image-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

/* 图片预览头部 */
.image-preview-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.preview-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-controls {
  display: flex;
  gap: 8px;
}

.image-controls .el-button {
  padding: 8px 12px;
  font-size: 12px;
}

/* 图片预览内容区域 */
.image-preview-content {
  flex: 1;
  position: relative;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  min-height: 400px;
}

.image-wrapper {
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

.preview-image:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 加载状态 */
.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.image-loading .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

/* 错误状态 */
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #f56c6c;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.6;
}

.image-error p {
  margin: 0 0 16px 0;
}

/* 图片信息 */
.image-info {
  padding: 12px 20px;
  background: #ffffff;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 24px;
  flex-shrink: 0;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.info-item .label {
  color: #909399;
  margin-right: 4px;
}

.info-item .value {
  color: #2c3e50;
  font-weight: 500;
}

/* 预览空状态 */
.preview-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  background: #f5f7fa;
}

.preview-empty .empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.preview-empty .empty-text {
  font-size: 16px;
  font-weight: 500;
}

/* 全屏预览样式 */
::v-deep .fullscreen-image-dialog {
  .el-dialog {
    margin: 0 !important;
    border-radius: 8px;
    overflow: hidden;
  }

  .el-dialog__header {
    padding: 16px 20px;
    background: #f8fafc;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    background: #f8fafc;
    border-top: 1px solid #ebeef5;
    text-align: center;
  }
}

.fullscreen-image-container {
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000;
  overflow: hidden;
  position: relative;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  transform-origin: center center;
}

.fullscreen-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.zoom-display {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  min-width: 50px;
  text-align: center;
}

/* 深色主题下的图片预览样式 */
html[data-theme='dark'] {
  .image-preview-container {
    background: #1A2E52;
  }

  .image-preview-header {
    background: #162549;
    border-bottom-color: #4F98F6;
  }

  .preview-title {
    color: #ffffff;
  }

  .image-preview-content {
    background: #0c1324;
  }

  .image-loading {
    color: #B3D3E5;
  }

  .image-error {
    color: #F74A4D;
  }

  .image-info {
    background: #1A2E52;
    border-top-color: #4F98F6;
  }

  .info-item .label {
    color: #B3D3E5;
  }

  .info-item .value {
    color: #ffffff;
  }

  .preview-empty {
    background: #0c1324;
    color: #B3D3E5;
  }

  .fullscreen-image-container {
    background: #000000;
  }

  .zoom-display {
    color: #ffffff;
  }

  ::v-deep .fullscreen-image-dialog {
    .el-dialog__header {
      background: #162549;
      border-bottom-color: #4F98F6;
    }

    .el-dialog__footer {
      background: #162549;
      border-top-color: #4F98F6;
    }
  }
}

/* 浅色主题下的图片预览样式 */
[data-theme="tint"],
[data-theme="defaule"] {
  .image-preview-container {
    background: #ffffff;
  }

  .image-preview-header {
    background: #F8FAFC;
    border-bottom-color: #EAEFF5;
  }

  .preview-title {
    color: #2E3641;
  }

  .image-preview-content {
    background: #F8FAFC;
  }

  .image-loading {
    color: #97A0AA;
  }

  .image-error {
    color: #f56c6c;
  }

  .image-info {
    background: #ffffff;
    border-top-color: #EAEFF5;
  }

  .info-item .label {
    color: #97A0AA;
  }

  .info-item .value {
    color: #2E3641;
  }

  .preview-empty {
    background: #F8FAFC;
    color: #97A0AA;
  }

  .zoom-display {
    color: #2E3641;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .preview-title {
    max-width: 100%;
  }

  .image-controls {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .image-info {
    flex-direction: column;
    gap: 8px;
  }

  .fullscreen-controls {
    flex-wrap: wrap;
    gap: 8px;
  }

  ::v-deep .fullscreen-image-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 2.5vh auto !important;
    }

    .el-dialog__body {
      height: 60vh;
    }
  }

  .fullscreen-image-container {
    height: 60vh;
  }
}
</style>
